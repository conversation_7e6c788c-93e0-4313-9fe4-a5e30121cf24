{"id": "yuchai-report-api", "name": "玉柴报表API Mock", "description": "玉柴Vue报表车间工作台接口Mock配置", "enabled": true, "routes": [{"id": "vue-report-caption-workshop", "method": "POST", "path": "/yuchai_api/vue_report/zhen<PERSON>_vue_report_caption_workshop", "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": {"code": 200, "message": "同步成功", "data": {"results": [{"sequence": "{{number.random.40-60}}", "id": "{{number.random.90-130}}", "manufacturing_order_number": "WH/MO/{{number.random.10000-99999}}", "figure_number_name": "{{array.pick.ECU支撑板组件,支托焊接组件,调节板,前吊耳（成品）,曲轴皮带轮（成品）,曲轴皮带轮（毛坯）,曲轴前油封座（成品）,涡轮后接管（成品）,喷油泵连接盘（成品）,支架,支撑板,气缸体盖板Ⅱ,气缸体盖板Ⅰ,拉杆,加强板,支承板}}", "figure_number": "{{faker.string.alphanumeric}}-{{number.random.1000000-9999999}}{{array.pick.A,B,C,D,M}}", "planned_production_quantity": "{{number.random.50-1000}}.0", "product_category": "{{array.pick.成品 / 焊接件,成品 / 铸铁件,半成品/铸铁件/毛坯,半成品 / 铸铁件 / 毛坯}}", "first_date_start": "{{date.add.days.{{number.random.0-10}}}}", "first_quantity": "{{number.random.50-500}}", "mprk_quantity": "{{number.random.10-400}}", "completion_rate_percent": "{{number.random.5-95}}%", "quality_status_identification": "{{array.pick.合格,待检,不合格}}", "first_operation_time": "{{number.random.6-23}}:{{number.random.10-59}}", "planned_stock_in_time": "{{date.add.days.{{number.random.1-15}}}} {{number.random.6-23}}:{{number.random.10-59}}", "state": "{{array.pick.,待关闭,完成,进行中}}"}], "total": "{{number.random.150-300}}"}}}, "delay": {"min": 200, "max": 800}}]}