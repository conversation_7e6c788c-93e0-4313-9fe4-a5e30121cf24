import fs from 'fs';
import path from 'path';
import { faker } from '@faker-js/faker';
import { MockConfig, MockRoute, MockRequest, MockResponse } from '../types';
import { loadJsonFile, getJsonFiles, matchesConditions, generateDelay, applyDelay } from '../utils';
import config from '../config';

export class MockService {
  private mockConfigs: Map<string, MockConfig> = new Map();
  private routes: Map<string, MockRoute[]> = new Map();

  constructor() {
    this.loadMockConfigs();
  }

  /**
   * Load all mock configurations from files
   */
  public loadMockConfigs(): void {
    console.log(`Loading mock configurations from: ${config.mock.configPath}`);
    
    const configFiles = getJsonFiles(config.mock.configPath);
    this.mockConfigs.clear();
    this.routes.clear();

    configFiles.forEach(filePath => {
      const mockConfig = loadJsonFile<MockConfig>(filePath);
      if (mockConfig && mockConfig.enabled) {
        this.mockConfigs.set(mockConfig.id, mockConfig);
        this.registerRoutes(mockConfig);
        console.log(`Loaded mock config: ${mockConfig.name} (${mockConfig.routes.length} routes)`);
      }
    });

    console.log(`Total loaded configs: ${this.mockConfigs.size}`);
  }

  /**
   * Register routes for a mock configuration
   */
  private registerRoutes(config: MockConfig): void {
    config.routes.forEach(route => {
      const key = `${route.method}:${route.path}`;
      if (!this.routes.has(key)) {
        this.routes.set(key, []);
      }
      this.routes.get(key)!.push(route);
    });
  }

  /**
   * Find matching route for request
   */
  public findMatchingRoute(method: string, path: string, req: MockRequest): MockRoute | null {
    const key = `${method.toUpperCase()}:${path}`;
    const routes = this.routes.get(key);
    
    if (!routes) {
      // Try to find routes with path parameters
      for (const [routeKey, routeList] of this.routes.entries()) {
        const [routeMethod, routePath] = routeKey.split(':');
        if (routeMethod === method.toUpperCase() && this.matchesPath(routePath, path)) {
          const matchingRoutes = routeList.filter(route => 
            !route.conditions || matchesConditions(req, route.conditions)
          );
          if (matchingRoutes.length > 0) {
            return matchingRoutes[0];
          }
        }
      }
      return null;
    }

    // Find route that matches conditions
    const matchingRoute = routes.find(route => 
      !route.conditions || matchesConditions(req, route.conditions)
    );

    return matchingRoute || routes[0]; // Return first route if no conditions match
  }

  /**
   * Check if route path matches request path (with parameters)
   */
  private matchesPath(routePath: string, requestPath: string): boolean {
    const routeSegments = routePath.split('/');
    const requestSegments = requestPath.split('/');

    if (routeSegments.length !== requestSegments.length) {
      return false;
    }

    return routeSegments.every((segment, index) => {
      if (segment.startsWith(':')) {
        return true; // Parameter segment matches anything
      }
      return segment === requestSegments[index];
    });
  }

  /**
   * Process mock response
   */
  public async processMockResponse(route: MockRoute, req: MockRequest): Promise<MockResponse> {
    // Apply delay if configured
    if (route.delay) {
      const delay = generateDelay(route.delay);
      await applyDelay(delay);
    }

    // Clone response to avoid modifying original
    const response = { ...route.response };

    // Process response body templates
    if (response.body !== undefined) {
      response.body = this.processTemplateInObject(response.body, req);
    }

    // Load response from file if specified
    if (response.file) {
      try {
        const filePath = path.resolve(config.mock.configPath, response.file);
        if (fs.existsSync(filePath)) {
          const fileContent = fs.readFileSync(filePath, 'utf-8');
          if (response.file.endsWith('.json')) {
            response.body = JSON.parse(fileContent);
          } else {
            response.body = fileContent;
          }
        }
      } catch (error) {
        console.error(`Error loading response file ${response.file}:`, error);
      }
    }

    return response;
  }

  /**
   * Process templates in any object (recursive)
   */
  private processTemplateInObject(obj: any, req: MockRequest): any {
    if (typeof obj === 'string') {
      return this.processTemplate(obj, req);
    }

    if (Array.isArray(obj)) {
      // Check if this is a template array that should be repeated
      if (obj.length === 1 && typeof obj[0] === 'object' && obj[0]['{{repeat}}']) {
        const template = obj[0];
        const repeatCount = this.processTemplate(template['{{repeat}}'], req);
        const count = parseInt(repeatCount, 10) || 1;

        // Remove the repeat directive from template
        const itemTemplate = { ...template };
        delete itemTemplate['{{repeat}}'];

        // Generate array with specified count
        const result = [];
        for (let i = 0; i < count; i++) {
          result.push(this.processTemplateInObject(itemTemplate, req));
        }
        return result;
      }

      return obj.map(item => this.processTemplateInObject(item, req));
    }

    if (obj && typeof obj === 'object') {
      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        result[key] = this.processTemplateInObject(value, req);
      }
      return result;
    }

    return obj;
  }

  /**
   * Process template strings in response body
   */
  private processTemplate(template: string, req: MockRequest): string {
    return template.replace(/\{\{([^}]+)\}\}/g, (match, expression) => {
      try {
        // Simple template processing - can be extended
        const trimmed = expression.trim();

        if (trimmed.startsWith('query.')) {
          const key = trimmed.substring(6);
          return req.query[key] || '';
        }

        if (trimmed.startsWith('body.')) {
          const key = trimmed.substring(5);
          return req.body?.[key] || '';
        }

        if (trimmed.startsWith('params.')) {
          const key = trimmed.substring(7);
          return req.params[key] || '';
        }

        if (trimmed === 'timestamp') {
          return new Date().toISOString();
        }

        if (trimmed === 'random') {
          return Math.random().toString();
        }

        // Faker.js support
        if (trimmed.startsWith('faker.')) {
          return this.processFakerExpression(trimmed);
        }

        // Date helpers
        if (trimmed.startsWith('date.')) {
          return this.processDateExpression(trimmed);
        }

        // DateTime helpers
        if (trimmed.startsWith('datetime.')) {
          return this.processDateTimeExpression(trimmed);
        }

        // Number helpers
        if (trimmed.startsWith('number.')) {
          return this.processNumberExpression(trimmed);
        }

        // Array helpers
        if (trimmed.startsWith('array.')) {
          return this.processArrayExpression(trimmed);
        }

        return match; // Return original if no match
      } catch (error) {
        console.error(`Error processing template expression ${expression}:`, error);
        return match;
      }
    });
  }

  /**
   * Process faker.js expressions
   */
  private processFakerExpression(expression: string): string {
    try {
      const fakerPath = expression.substring(6); // Remove 'faker.'
      const parts = fakerPath.split('.');

      if (parts.length < 2) {
        return expression;
      }

      const category = parts[0];
      const method = parts[1];
      const args = parts.slice(2);

      // Access faker methods dynamically
      const fakerCategory = (faker as any)[category];
      if (!fakerCategory || typeof fakerCategory[method] !== 'function') {
        return expression;
      }

      // Call the faker method
      const result = fakerCategory[method](...args);
      return typeof result === 'string' ? result : String(result);
    } catch (error) {
      console.error(`Error processing faker expression ${expression}:`, error);
      return expression;
    }
  }

  /**
   * Process date expressions
   */
  private processDateExpression(expression: string): string {
    try {
      const dateCommand = expression.substring(5); // Remove 'date.'
      const now = new Date();

      switch (dateCommand) {
        case 'now':
          return now.toISOString();
        case 'today':
          return now.toISOString().split('T')[0];
        case 'tomorrow':
          const tomorrow = new Date(now);
          tomorrow.setDate(tomorrow.getDate() + 1);
          return tomorrow.toISOString().split('T')[0];
        case 'yesterday':
          const yesterday = new Date(now);
          yesterday.setDate(yesterday.getDate() - 1);
          return yesterday.toISOString().split('T')[0];
        default:
          // Support date.add.days.N or date.subtract.days.N
          if (dateCommand.startsWith('add.days.') || dateCommand.startsWith('subtract.days.')) {
            const parts = dateCommand.split('.');
            const operation = parts[0];
            const days = parseInt(parts[2], 10);
            if (!isNaN(days)) {
              const targetDate = new Date(now);
              if (operation === 'add') {
                targetDate.setDate(targetDate.getDate() + days);
              } else {
                targetDate.setDate(targetDate.getDate() - days);
              }
              return targetDate.toISOString().split('T')[0];
            }
          }
          return expression;
      }
    } catch (error) {
      console.error(`Error processing date expression ${expression}:`, error);
      return expression;
    }
  }

  /**
   * Process datetime expressions
   */
  private processDateTimeExpression(expression: string): string {
    try {
      const dateTimeCommand = expression.substring(9); // Remove 'datetime.'
      const now = new Date();

      switch (dateTimeCommand) {
        case 'now':
          return now.toISOString().replace('T', ' ').substring(0, 19);
        case 'today':
          const today = new Date(now);
          today.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), 0, 0);
          return today.toISOString().replace('T', ' ').substring(0, 19);
        default:
          // Support datetime.add.days.N or datetime.subtract.days.N
          if (dateTimeCommand.startsWith('add.days.') || dateTimeCommand.startsWith('subtract.days.')) {
            const parts = dateTimeCommand.split('.');
            const operation = parts[0];
            const days = parseInt(parts[2], 10);
            if (!isNaN(days)) {
              const targetDate = new Date(now);
              if (operation === 'add') {
                targetDate.setDate(targetDate.getDate() + days);
              } else {
                targetDate.setDate(targetDate.getDate() - days);
              }
              // Add random time
              targetDate.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), 0, 0);
              return targetDate.toISOString().replace('T', ' ').substring(0, 19);
            }
          }
          return expression;
      }
    } catch (error) {
      console.error(`Error processing datetime expression ${expression}:`, error);
      return expression;
    }
  }

  /**
   * Process number expressions
   */
  private processNumberExpression(expression: string): string {
    try {
      const numberCommand = expression.substring(7); // Remove 'number.'

      if (numberCommand.startsWith('random.')) {
        const range = numberCommand.substring(7); // Remove 'random.'
        if (range.includes('-')) {
          const [min, max] = range.split('-').map(n => parseInt(n.trim(), 10));
          if (!isNaN(min) && !isNaN(max)) {
            return String(Math.floor(Math.random() * (max - min + 1)) + min);
          }
        }
      }

      if (numberCommand === 'random') {
        return String(Math.floor(Math.random() * 1000));
      }

      return expression;
    } catch (error) {
      console.error(`Error processing number expression ${expression}:`, error);
      return expression;
    }
  }

  /**
   * Process array expressions
   */
  private processArrayExpression(expression: string): string {
    try {
      const arrayCommand = expression.substring(6); // Remove 'array.'

      if (arrayCommand.startsWith('pick.')) {
        const items = arrayCommand.substring(5).split(',').map(item => item.trim());
        if (items.length > 0) {
          return items[Math.floor(Math.random() * items.length)];
        }
      }

      return expression;
    } catch (error) {
      console.error(`Error processing array expression ${expression}:`, error);
      return expression;
    }
  }

  /**
   * Get all mock configurations
   */
  public getMockConfigs(): MockConfig[] {
    return Array.from(this.mockConfigs.values());
  }

  /**
   * Get mock configuration by ID
   */
  public getMockConfig(id: string): MockConfig | undefined {
    return this.mockConfigs.get(id);
  }

  /**
   * Reload configurations
   */
  public reload(): void {
    this.loadMockConfigs();
  }
}
