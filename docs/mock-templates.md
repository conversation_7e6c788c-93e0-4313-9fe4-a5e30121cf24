# Mock模板功能说明

## 概述

yc-mock-server现在支持强大的动态数据生成功能，可以在每次请求时生成不同的mock数据，而不是返回固定的静态JSON。

## 支持的模板语法

### 1. 基础模板

- `{{timestamp}}` - 当前时间戳 (ISO格式)
- `{{random}}` - 随机数
- `{{query.参数名}}` - 获取查询参数
- `{{body.字段名}}` - 获取请求体字段
- `{{params.参数名}}` - 获取路径参数

### 2. 数字生成

- `{{number.random}}` - 随机数 (0-1000)
- `{{number.random.10-100}}` - 指定范围的随机数

### 3. 日期生成

- `{{date.now}}` - 当前日期时间
- `{{date.today}}` - 今天日期
- `{{date.tomorrow}}` - 明天日期
- `{{date.yesterday}}` - 昨天日期
- `{{date.add.days.5}}` - 当前日期加5天
- `{{date.subtract.days.3}}` - 当前日期减3天

### 4. 数组选择

- `{{array.pick.选项1,选项2,选项3}}` - 从数组中随机选择一个

### 5. Faker.js支持

- `{{faker.string.alphanumeric}}` - 随机字母数字字符串
- `{{faker.person.firstName}}` - 随机名字
- `{{faker.company.name}}` - 随机公司名
- `{{faker.internet.email}}` - 随机邮箱
- 更多faker.js方法请参考官方文档

### 6. 动态数组生成

使用 `{{repeat}}` 指令可以动态生成数组：

```json
{
  "results": [
    {
      "{{repeat}}": "{{number.random.5-10}}",
      "id": "{{number.random.1-1000}}",
      "name": "{{faker.person.firstName}}"
    }
  ]
}
```

这将生成5-10个随机数量的记录，每个记录都有不同的id和name。

## 使用示例

### 车间工作台数据示例

```json
{
  "code": 200,
  "message": "同步成功",
  "data": {
    "results": [
      {
        "{{repeat}}": "{{number.random.8-15}}",
        "sequence": "{{number.random.40-60}}",
        "id": "{{number.random.90-130}}",
        "manufacturing_order_number": "WH/MO/{{number.random.10000-99999}}",
        "figure_number_name": "{{array.pick.ECU支撑板组件,支托焊接组件,调节板,前吊耳（成品）}}",
        "planned_production_quantity": "{{number.random.50-1000}}.0",
        "completion_rate_percent": "{{number.random.5-95}}%",
        "first_date_start": "{{date.add.days.{{number.random.0-10}}}}",
        "state": "{{array.pick.,待关闭,完成,进行中}}"
      }
    ],
    "total": "{{number.random.150-300}}"
  }
}
```

## 配置文件结构

Mock配置文件支持两种响应方式：

### 1. 静态文件引用

```json
{
  "response": {
    "status": 200,
    "headers": {
      "Content-Type": "application/json"
    },
    "file": "static-data.json"
  }
}
```

### 2. 动态模板响应

```json
{
  "response": {
    "status": 200,
    "headers": {
      "Content-Type": "application/json"
    },
    "body": {
      "code": 200,
      "message": "{{array.pick.成功,同步成功,操作成功}}",
      "data": {
        "timestamp": "{{timestamp}}",
        "random_id": "{{number.random.1000-9999}}"
      }
    }
  }
}
```

## 优势

1. **动态数据**: 每次请求返回不同的数据，更真实地模拟生产环境
2. **灵活配置**: 支持复杂的数据结构和嵌套模板
3. **易于维护**: 无需手动维护大量静态数据文件
4. **高度可定制**: 支持自定义数据范围和格式
5. **性能优化**: 实时生成，无需预先准备大量数据

## 注意事项

1. 模板表达式必须用双大括号包围：`{{expression}}`
2. 嵌套模板表达式会按顺序解析
3. 数组选择时用逗号分隔选项，不要有空格
4. 日期格式默认为ISO格式，时间部分可以通过字符串截取调整
5. `{{repeat}}` 指令只能用在数组的第一个元素中
